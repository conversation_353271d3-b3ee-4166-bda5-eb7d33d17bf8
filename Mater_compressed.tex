\documentclass[a4paper,10pt]{article}
\usepackage{latexsym}
\usepackage[margin=0.4in]{geometry} % ← even tighter margins
\usepackage{titlesec}
\usepackage{marvosym}
\usepackage{enumitem}
\usepackage[hidelinks]{hyperref}
\usepackage[english]{babel}
\usepackage{tabularx}
\usepackage{fancyhdr}
\usepackage{xcolor}

% Adjust page margins
\pagestyle{fancy}
\fancyhf{} % clear all header and footer fields
\renewcommand{\headrulewidth}{0pt}
\renewcommand{\footrulewidth}{0pt}

% Section formatting - reduced spacing
\titleformat{\section}{\large\bfseries}{}{0em}{}[\titlerule]
\titlespacing{\section}{0pt}{6pt}{2pt}

% Custom commands with reduced spacing
\newcommand{\resumeItem}[2]{\item\textbf{#1}{: #2}\vspace{-3pt}}
\newcommand{\resumeSubheading}[4]{
  \vspace{-1pt}\item
    \textbf{#1} \hfill {\small #2} \\
    \textit{\small #3} \hfill {\small #4} \vspace{-6pt}
}
\newcommand{\resumeSubItem}[2]{\item[$\circ$] \textbf{#1}: #2\vspace{-2pt}}
\renewcommand{\labelitemii}{$\circ$}

\newcommand{\resumeSection}[2]{
  \section*{\uppercase{#1}}
  \begin{itemize}[leftmargin=0.1in, label={}, itemsep=-2pt]
    #2
  \end{itemize}
}

\newcommand{\resumeSubHeadingListStart}{\begin{itemize}[leftmargin=0.1in, label={}, itemsep=-1pt]} 
\newcommand{\resumeSubHeadingListEnd}{\end{itemize}}

% Reduce line spacing globally
\linespread{0.95}

%-----------------------------------------------------------
%               BEGIN RESUME
%-----------------------------------------------------------
\begin{document}

\begin{center}
  \textbf{{\Huge Pankaj Kumar}} \\[0.5ex]
  \href{mailto:<EMAIL>}{<EMAIL>} \ $|$ \ +91-8219499324 \ $|$ \ 
  \href{https://www.linkedin.com/in/pankaj-full-stack/}{LinkedIn} \ $|$ \ Mohali, India
\end{center}

% ----------- SUMMARY -----------
\resumeSection{Summary}{
  \item Software Engineer with 3.6 years specializing in backend development for Generative AI and Web3 solutions. Expert in Node.js, React.js, Solidity with proven track record in architecting scalable infrastructures, integrating LLMs via RAG/LangChain, and delivering decentralized applications on cloud and blockchain platforms.
}

% ----------- SKILLS -----------
\resumeSection{Technical Skills}{
  \item \textbf{Languages}: Python, JavaScript, HTML, CSS, Solidity
  \item \textbf{AI/ML}: LLMs, LangChain, RAG, Guardrails, Agentic AI, Vector DBs, n8n, NLP
  \item \textbf{Frontend}: React.js, React Native, Redux, npm
  \item \textbf{Backend}: Node.js, Express.js, Flask, FastAPI, MongoDB, SQL, Redis, Postgres
  \item \textbf{Cloud/DevOps}: AWS (S3, Lambda, EC2), Docker, Serverless Framework
  \item \textbf{Web3}: Hardhat, Ethers.js, Smart Contracts, Tokenomics
}

% ----------- EXPERIENCE -----------
\resumeSection{Experience}{
  \resumeSubHeadingListStart

\resumeSubheading
  {Antier Solutions Pvt Ltd}{July 2024 -- Present}
  {Associate Software Engineer (AI and Blockchain)}{Mohali, Punjab}
  
  \begin{itemize}[itemsep=-2pt]
    \item Developed secure smart contracts using Solidity, Hardhat, Ethers.js for production blockchain solutions
    \item Architected AI-driven products using LangChain, RAG, Guardrails, VectorDBs, and n8n workflows
    \item Spearheaded AI development stream launch, establishing processes and infrastructure from scratch
    \item Integrated AI into \href{https://www.abstraxn.com/}{\underline{Abstraxn}} and released open-source SDK \href{https://www.npmjs.com/package/doc_chat_ai}{\underline{\texttt{doc\_chat\_ai}}}
  \end{itemize}

  \resumeSubheading
    {Zoptal Solutions Pvt Ltd}{2022 -- 2024}
    {MERN Stack Developer}{Mohali, Punjab}
      
    \begin{itemize}[itemsep=-2pt]
      \item Developed scalable MERN stack applications with Node.js, React.js, MongoDB, deploying on AWS EC2
      \item Designed RESTful APIs and MVC architecture from project inception to production launch
      \item Implemented real-time chat using socket technology and enhanced API performance
      \item Built user interfaces with Bootstrap/Material UI, collaborating with cross-functional teams
    \end{itemize}

  \resumeSubHeadingListEnd
}

% ----------- PROJECTS -----------
\resumeSection{Projects}{
  \resumeSubHeadingListStart

  \resumeSubheading
  {Ozolio - Smart Contract Developer}{2024}
  {Ethereum Token Ecosystem}{}
  \begin{itemize}[itemsep=-2pt]
    \item Developed production-ready smart contracts for live streaming platform with tokenomics and vesting
    \item \href{https://etherscan.io/address/******************************************#code}{\underline{Etherscan}} | \href{https://ozoliotoken.io/}{\underline{ozoliotoken.io}}
  \end{itemize}

\resumeSubheading
  {Coin De Casa - Smart Contract Developer}{2025}
  {Real Estate Tokenization}{}
  \begin{itemize}[itemsep=-2pt]
    \item Engineered smart contracts for affordable housing platform with asset-backed tokenomics
    \item \href{https://coindecasa.com/}{\underline{coindecasa.com}}
  \end{itemize}

\resumeSubheading
  {Abstraxn - AI Full Stack Developer}{2024}
  {RAG System for Client Websites}{}
  \begin{itemize}[itemsep=-2pt]
    \item Built RAG system enabling "ASK AI" on websites using FastAPI, Qdrant, OpenRouter LLM
    \item Automated workflows via n8n, deployed with Docker/AWS, integrated with Web3 platform
    \item \href{https://www.linkedin.com/posts/pankaj-full-stack_ai-rag-chatbot-activity-7346106603563032577-PPkb}{\underline{Demo}}
  \end{itemize}

  \resumeSubheading
  {web3AI - AI and Web3 Platform}{2024}
  {Natural Language Blockchain Interface}{}
  \begin{itemize}[itemsep=-2pt]
    \item Built AI platform for 500+ wallets interaction using natural language commands
    \item Integrated ChatGPT/LLM for blockchain operations: balances, gas fees, token swaps, smart contracts
    \item \href{https://www.linkedin.com/posts/pankaj-full-stack_blockchain-ai-activity-7334973774879985664-O4IK}{\underline{Announcement}}
  \end{itemize}

\resumeSubheading
  {wetango - Backend Developer}{2023}
  {Dating App Backend (Hapi.js)}{}
  \begin{itemize}[itemsep=-2pt]
    \item Resolved major production bugs, integrated APIs for real-time chat and matching algorithms
    \item \href{https://play.google.com/store/apps/details?id=com.zoptal.wetango&hl=en_IN}{Google Play}
  \end{itemize}

\resumeSubheading
  {autoaide - Backend Developer}{2022}
  {Vehicle Service Platform}{}
  \begin{itemize}[itemsep=-2pt]
    \item Engineered scalable backend with Node.js, Express.js, MongoDB on AWS EC2
    \item \href{https://play.google.com/store/apps/details?id=com.zoptal.autoaide&hl=en_IN}{Google Play}
  \end{itemize}
      
  \resumeSubHeadingListEnd
}

% ----------- EDUCATION -----------
\resumeSection{Education}{
  \resumeSubHeadingListStart
    \resumeSubheading
      {B.Tech in Mechanical Engineering}{2014 – 2018}
      {Chandigarh Engineering College}{Mohali, India}
  \resumeSubHeadingListEnd
}

% ----------- STRENGTHS -----------
\resumeSection{Professional Strengths}{
  \item Rapid Problem Solving • End-to-End Project Ownership • Fast Technology Adaptability • Cross-Functional Collaboration • Clear Technical Communication • Deadline-Driven Delivery • Innovation Focus • Security Best Practices
}

\end{document}