\documentclass[a4paper,10pt]{article}
\usepackage{latexsym}
\usepackage[margin=0.5in]{geometry} % ← tighter margins
\usepackage{titlesec}
\usepackage{marvosym}
\usepackage{enumitem}
\usepackage[hidelinks]{hyperref}
\usepackage[english]{babel}
\usepackage{tabularx}
\usepackage{fancyhdr}
\usepackage{xcolor}

% Adjust page margins
\pagestyle{fancy}
\fancyhf{} % clear all header and footer fields
\renewcommand{\headrulewidth}{0pt}
\renewcommand{\footrulewidth}{0pt}

% Section formatting
\titleformat{\section}{\large\bfseries}{}{0em}{}[\titlerule]
\titlespacing{\section}{0pt}{10pt}{5pt}

% Custom commands
\newcommand{\resumeItem}[2]{\item\textbf{#1}{: #2}\vspace{-2pt}}
\newcommand{\resumeSubheading}[4]{
  \vspace{1pt}\item
    \textbf{#1} \hfill {\small #2} \\
    \textit{\small #3} \hfill {\small #4} \vspace{-4pt}
}
\newcommand{\resumeSubItem}[2]{\item[$\circ$] \textbf{#1}: #2}
\renewcommand{\labelitemii}{$\circ$}

\newcommand{\resumeSection}[2]{
  \section*{\uppercase{#1}}
  \begin{itemize}[leftmargin=0.15in, label={}]
    #2
  \end{itemize}
}

\newcommand{\resumeSubHeadingListStart}{\begin{itemize}[leftmargin=0.15in, label={}]} 
\newcommand{\resumeSubHeadingListEnd}{\end{itemize}}

%-----------------------------------------------------------
%               BEGIN RESUME
%-----------------------------------------------------------
\begin{document}

\begin{center}
  \textbf{{\Huge Pankaj Kumar}} \\[1ex]
  \href{mailto:<EMAIL>}{<EMAIL>} \ $|$ \ +91-8219499324 \ $|$ \ 
  \href{https://www.linkedin.com/in/pankaj-full-stack/}{LinkedIn} \ $|$ \ Mohali, India
\end{center}


% ----------- SUMMARY -----------
\resumeSection{Summary}{
  \item Software Engineer with 3.6 years of experience specializing in backend development for Generative AI and Web3 (Smart Contracts) solutions, leveraging advanced stacks including Node.js, React.js, and Solidity. Proven track record in architecting and deploying scalable backend infrastructures, integrating LLMs via RAG/LangChain, and delivering decentralized applications on cloud and blockchain platforms. Known for rapid adaptability and seamless cross-stack integration to drive end-to-end project success.
}

% ----------- SKILLS -----------
\resumeSection{Technical Skills}{
  \item \textbf{Programming Languages}: Python, JavaScript (ES6+), HTML, CSS
  \item \textbf{AI \& ML}: LLMs, n8n, LangChain, RAG, Guardrails, Agentic AI, LangGraph, Vector DBs, NLP
  \item \textbf{Front-end}: React.js, React Native (iOS/Android), Redux, React Hooks, npm
  \item \textbf{Back-end}: Node.js, Express.js, Flask, FastAPI, MongoDB, SQL, Redis, Kafka, Postgres, TypeORM, stripe
  \item \textbf{APIs \& Testing}: RESTful APIs, Postman, Swagger, Insomnia, Jest, Supertest
  \item \textbf{Cloud \& DevOps}: AWS (S3, Lambda, API Gateway, EC2, IAM), Serverless Framework, Docker, GitHub, Bitbucket
  \item \textbf{Web3}: Solidity, Hardhat, Ethers.js, Remix, Smart Contracts, ICOs, Tokenomics
  \item \textbf{Tools \& Management}: Jira, Confluence, VS Code, Xcode, Android Studio, Cursor, Augment Code
}

% ----------- EXPERIENCE -----------
\resumeSection{Experience}{
  \resumeSubHeadingListStart

\resumeSubheading
  {Antier Solutions Pvt Ltd}{July 2024 -- Present}
  {Associate Software Engineer (AI and Blockchain)}{Mohali, Punjab}
  
  \vspace{1em} 
  
  \begin{itemize}
    \item \textbf{Smart Contract Development:} Developed and deployed secure, production-ready smart contracts using Solidity, Hardhat, and Ethers.js for blockchain-based solutions.
    
    \item \textbf{AI Full Stack Solutions:} Architected backend and frontend components for AI-driven products using LangChain, Retrieval-Augmented Generation (RAG), Guardrails, VectorDBs, NLP, and n8n.
    
    \item \textbf{AI Initiative Launch:} Spearheaded the launch of the AI development stream—establishing processes, tools, and infrastructure from scratch to deploy production-ready AI solutions.
    
    \item \textbf{Product Collaboration – \href{https://www.abstraxn.com/}{\underline{Abstraxn}}:} Integrated advanced AI capabilities into the in-house product \textit{Abstraxn}, and released the open-source SDK \href{https://www.npmjs.com/package/doc_chat_ai}{\underline{\texttt{doc\_chat\_ai}}} to enable broader adoption.
    
    \item \textbf{Cross-Team Delivery:} Collaborated across blockchain, AI, and web teams to deliver robust, innovative features under tight deadlines.
  \end{itemize}


  \resumeSubheading
    {Zoptal Solutions Pvt Ltd}{2022 -- 2024}
    {MERN Stack Developer}{Mohali, Punjab}
      \vspace{1em} 
      
    \begin{itemize}
      \item Developed scalable and efficient web applications using MERN stack technologies including Node.js, Express.js, React.js, and MongoDB, deploying production systems on AWS EC2.
      \item Started as a core backend developer, designing RESTful APIs, setting up MVC architecture, and integrating third-party libraries from project inception to production launch.
      \item Implemented dynamic features such as real-time chat applications using socket-based technology for enhanced user interaction.
      \item Enhanced API performance, identified and resolved logical issues in company projects, and increased overall system reliability.
      \item Utilized AWS serverless services and S3 buckets to optimize data workflows and supported automation for deployment and scalability.
      \item Built user-friendly interfaces using design libraries like Bootstrap and Material UI, collaborating with cross-functional teams of designers, project managers, and clients to deliver high-impact solutions.
      \item Gained experience with additional technologies including NestJS, AWS IAM roles, and enhanced security and scalability of cloud applications.
    \end{itemize}



  \resumeSubHeadingListEnd
}



% ----------- PROJECTS -----------
\resumeSection{Projects}{
  \resumeSubHeadingListStart

  \subsection*{Blockchain Projects}
  
    \resumeSubheading
  {Ozolio}{2024}
  {Smart Contract Developer}{}
  \begin{itemize}
    \item Developed and deployed production-ready smart contracts for Ozolio’s token ecosystem on Ethereum, implementing tokenomics and vesting using Solidity.
    \item Architected secure token distribution for a live streaming and webcam hosting platform, supporting enterprise partnerships.
    \item Deployed and verified contract code: \href{https://etherscan.io/address/******************************************#code}{\underline{Etherscan}}; platform: \href{https://ozoliotoken.io/}{\underline{ozoliotoken}}
  \end{itemize}

\resumeSubheading
  {Coin De Casa}{2025}
  {Smart Contract Developer}{}
  \begin{itemize}
    \item Engineered and deployed smart contracts for the Coin De Casa token ecosystem, implementing robust tokenomics and vesting logic with Solidity.
    \item Contributed blockchain expertise to a real-world affordable housing platform, integrating property asset-backing and modular construction workflows.
    \item Supported platform goals to revolutionize global housing affordability and enable secure, asset-backed investment using blockchain technology.
    \item Platform: \href{https://coindecasa.com/}{\underline{coindecasa}}
  \end{itemize}

\resumeSubheading
  {Istakapaza}{2024}
  {MERN Stack Developer}{}
  \begin{itemize}
    \item Developed full stack features and fixed critical bugs for Istakapaza, a blockchain asset tokenization platform specializing in real estate.
    \item Integrated Gelato relayer services in the backend and implemented seamless frontend integration for asset management workflows.
    \item Platform: \href{https://www.istakapaza.com/#/}{\underline{istakapaza}}
  \end{itemize}

 \subsection*{AI Projects}

 \resumeSubheading
  {Abstraxn}{2024}
  {AI Full Stack Developer}{}
  \begin{itemize}
    \item Built and deployed a Retrieval-Augmented Generation (RAG) system enabling “ASK AI” on client websites, allowing users to query proprietary data using natural language.
    \item Designed backend APIs with Python, FastAPI, and Node.js; leveraged Qdrant vector database with multi-tenancy for secure client-specific document search.
    \item Integrated OpenRouter LLM for advanced response generation, coordinated with LangChain, and enforced compliance with Guardrails for output safety.
    \item Automated workflows via n8n, delivered frontend integration with React.js, and managed production deployment with Docker and AWS.
    \item Integrated the RAG solution with the in-house account abstraction platform \href{https://www.abstraxn.com/}{\underline{Abstraxn}}
 to extend seamless, secure AI capabilities for Web3 user journeys.
  
  \item Project demo: \href{https://www.linkedin.com/posts/pankaj-full-stack_ai-rag-chatbot-activity-7346106603563032577-PPkb?utm_source=share&utm_medium=member_desktop&rcm=ACoAACKxoJkBhPJmJ4Y7EOl3VU_sxm8dQPt9aLk}{\underline{RAG LinkedIn Post}}
\end{itemize}

  \resumeSubheading
  {web3AI}{2024}
  {AI and Web3 Platform Developer}{}
  \begin{itemize}
    \item Designed and built an AI-powered Web3 automation platform allowing users to interact with over 500 wallets (MetaMask, Coinbase, Trust, Safe) using natural language commands.
    \item Engineered secure backend workflows to fetch balances, check gas fees, track blockchain activity, send tokens, swap currencies, and deploy smart contracts with on-chain signature verification.
    \item Integrated conversational AI (ChatGPT/LLM) to seamlessly interpret user intents and execute blockchain operations, enhancing user experience for both technical and non-technical audiences.
    \item Laid the foundation for upcoming features like token bridges, smart automation, and account abstraction support.
    \item Project announcement: \href{https://www.linkedin.com/posts/pankaj-full-stack_blockchain-ai-activity-7334973774879985664-O4IK?utm_source=share&utm_medium=member_desktop&rcm=ACoAACKxoJkBhPJmJ4Y7EOl3VU_sxm8dQPt9aLk}{\underline{web3AI LinkedIn Post}}
  \end{itemize}

\subsection*{Backend Development}

\resumeSubheading
  {wetango}{2023}
  {Backend Developer}{}
  \begin{itemize}
    \item Contributed to the development and maintenance of the wetango dating app backend built on the Hapi.js server framework.
    \item Diagnosed and resolved major production bugs, significantly improving app stability and API reliability for thousands of users.
    \item Integrated RESTful APIs supporting real-time chat, matching algorithms, and secure authentication.
    \item Collaborated with mobile and frontend teams to deliver consistent user experiences across platforms.
    \item App: \href{https://play.google.com/store/apps/details?id=com.zoptal.wetango&hl=en_IN}{wetango (Google Play)}
  \end{itemize}

\resumeSubheading
  {autoaide}{2022}
  {Backend Developer}{}
  \begin{itemize}
    \item Engineered scalable backend systems for AutoAide, a comprehensive vehicle service platform, using Node.js, Express.js, and MongoDB.
    \item Designed and implemented key features for service booking, vehicle tracking, and notification workflows from scratch.
    \item Deployed, monitored, and maintained production servers on AWS EC2, ensuring high uptime and robust performance.
    \item Optimized database queries and API endpoints, reducing response latency and enhancing user experience.
    \item App: \href{https://play.google.com/store/apps/details?id=com.zoptal.autoaide&hl=en_IN}{AutoAide (Google Play)}
  \end{itemize}

      
  \resumeSubHeadingListEnd
}

% ----------- EDUCATION -----------
\resumeSection{Education}{
  \resumeSubHeadingListStart
    \resumeSubheading
      {B.Tech in Mechanical Engineering}{2014 – 2018}
      {Chandigarh Engineering College}{Mohali, India}
  \resumeSubHeadingListEnd
}
% ----------- Survival skills -----------
\resumeSection{Professional Strengths}{
  \item Rapid Problem Solving in High-Pressure Environments
  \item End-to-End Project Ownership: Design to Deployment
  \item Fast Learning and Technology Adaptability
  \item Effective Collaboration with Cross-Functional Teams
  \item Clear Technical and Non-Technical Communication
  \item Resilience and Reliability Under Tight Deadlines
  \item Innovation in Product Development and Process Improvement
  \item Security Best Practices in Cloud and Blockchain Solutions
  \item Strong Focus on User Experience and Issue Resolution
}


\end{document}
